.tableCell() {
  padding: 6px 13px;
  border: 1px solid #d1d9e0;
}

.chunkText() {
  em {
    color: red;
    font-style: normal;
  }
  table {
    width: 100%;
    box-sizing: border-box;
    border-collapse: collapse;
  }

  caption {
    color: @blurBackground;
    font-size: 14px;
    height: 20px;
    line-height: 20px;
    font-weight: 600;
    margin-bottom: 6px;
  }

  th {
    // color: #fff;
    // background-color: @blurBackground;
    .tableCell;
  }

  td {
    .tableCell;
  }

  td:hover {
    background: @blurBackgroundHover;
  }

  tr:nth-child(even) {
    background-color: #f2f2f22a;
  }
}

.pointerCursor() {
  cursor: pointer;
}

.clearCardBody() {
  :global {
    .ant-card-body {
      padding: 0;
      margin: 0;
    }
  }
}

.textEllipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multipleLineEllipsis(@line) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line;
  overflow: hidden;
  text-overflow: ellipsis;
}

.linkText() {
  border-radius: 6px;
  padding: 6px 10px;
  background-color: #eff8ff;
  border: 1px;
}

.commonNodeShadow() {
  box-shadow:
    -6px 0 12px 0 rgba(179, 177, 177, 0.08),
    -3px 0 6px -4px rgba(0, 0, 0, 0.12),
    -6px 0 16px 6px rgba(0, 0, 0, 0.05);
}

.commonNodeRadius() {
  border-radius: 10px;
}

.commonNode() {
  .commonNodeShadow();
  .commonNodeRadius();

  padding: 10px;
  background: white;
  width: 200px;
}
