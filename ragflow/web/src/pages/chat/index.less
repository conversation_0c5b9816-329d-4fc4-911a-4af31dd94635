.chatWrapper {
  height: 100%;
  width: 100%;

  .chatAppWrapper {
    width: 288px;
    padding: 26px;

    .chatAppContent {
      overflow-y: auto;
      width: 100%;
    }

    .chatAppCard {
      :global(.ant-card-body) {
        padding: 10px;
      }
      .cubeIcon {
        &:hover {
          cursor: pointer;
        }
      }
    }
    .chatAppCardSelected {
      :global(.ant-card-body) {
        background-color: @gray11;
        border-radius: 8px;
      }
    }
    .chatAppCardSelectedDark {
      :global(.ant-card-body) {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
      }
    }
  }
  .chatTitleWrapper {
    width: 220px;
    padding: 26px 0;
  }

  .chatTitle {
    padding: 5px 15px;
  }

  .chatTitleContent {
    padding: 5px 10px;
    overflow: auto;
  }

  .chatSpin {
    :global(.ant-spin-container) {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .chatTitleCard {
    :global(.ant-card-body) {
      padding: 8px;
    }
  }

  .chatTitleCardSelected {
    :global(.ant-card-body) {
      background-color: @gray11;
      border-radius: 8px;
    }
  }
  .chatTitleCardSelectedDark {
    :global(.ant-card-body) {
      background-color: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }
  }

  .divider {
    margin: 0;
    height: 100%;
  }
}
